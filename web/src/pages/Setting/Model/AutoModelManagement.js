import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, Input, Switch, Space, message, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { API, showError, showSuccess } from '../../../helpers';

export default function AutoModelManagement() {
  const [configs, setConfigs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [form] = Form.useForm();

  const loadConfigs = async () => {
    setLoading(true);
    try {
      const res = await API.get('/api/auto_models');
      if (res.data.success) {
        setConfigs(res.data.data);
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError('Failed to load auto model configurations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  const handleSubmit = async (values) => {
    const config = {
      name: values.name,
      description: values.description,
      is_active: values.is_active,
      models: values.models.split('\n').filter(m => m.trim() !== '')
    };

    try {
      if (editingConfig) {
        // Update existing config
        await API.put(`/api/auto_models/${editingConfig.name}`, config);
        showSuccess('Configuration updated successfully');
      } else {
        // Create new config
        await API.post('/api/auto_models', config);
        showSuccess('Configuration created successfully');
      }
      setModalVisible(false);
      loadConfigs();
    } catch (error) {
      showError('Failed to save configuration');
    }
  };

  const handleDelete = async (name) => {
    try {
      await API.delete(`/api/auto_models/${name}`);
      showSuccess('Configuration deleted successfully');
      loadConfigs();
    } catch (error) {
      showError('Failed to delete configuration');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <span style={{ color: isActive ? '#52c41a' : '#ff4d4f' }}>
          {isActive ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      title: 'Models',
      dataIndex: 'models',
      key: 'models',
      render: (models) => models.join(', '),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Button
            icon={<EditOutlined />}
            onClick={() => {
              setEditingConfig(record);
              form.setFieldsValue({
                ...record,
                models: record.models.join('\n')
              });
              setModalVisible(true);
            }}
          />
          <Popconfirm
            title="Are you sure to delete this configuration?"
            onConfirm={() => handleDelete(record.name)}
            okText="Yes"
            cancelText="No"
          >
            <Button danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="Auto Model Management"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingConfig(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            Add Configuration
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={configs}
          rowKey="name"
          loading={loading}
          pagination={false}
        />
      </Card>

      <Modal
        title={editingConfig ? 'Edit Configuration' : 'Add Configuration'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ is_active: true }}
        >
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please input a name' }]}
          >
            <Input disabled={!!editingConfig} />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea />
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="Status"
            valuePropName="checked"
          >
            <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
          </Form.Item>
          
          <Form.Item
            name="models"
            label="Models (one per line)"
            rules={[{ required: true, message: 'Please input at least one model' }]}
          >
            <Input.TextArea rows={5} placeholder="model1
model2
model3" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
