import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, Switch, message, Popconfirm, Space, Select } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { API, showError, showSuccess } from '../../helpers';

export default function AdminAutoModelManagement() {
  const [configs, setConfigs] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [form] = Form.useForm();

  const loadData = async () => {
    setLoading(true);
    try {
      // Load users
      const usersRes = await API.get('/api/admin/user/');
      if (usersRes.data.success) {
        setUsers(usersRes.data.data);
      } else {
        showError(usersRes.data.message || 'Failed to load users');
      }
      
      // Load configs
      const configsRes = await API.get('/api/admin/auto_models');
      if (configsRes.data.success) {
        setConfigs(configsRes.data.data || []);
      } else {
        showError(configsRes.data.message || 'Failed to load configurations');
      }
    } catch (error) {
      showError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleSubmit = async (values) => {
    const config = {
      ...values,
      models: values.models.split('\n').filter(m => m.trim() !== '')
    };

    try {
      if (editingConfig) {
        await API.put(`/api/admin/auto_models/${editingConfig.name}?user_id=${editingConfig.user_id}`, config);
        showSuccess('Updated successfully');
      } else {
        await API.post('/api/admin/auto_models', config);
        showSuccess('Created successfully');
      }
      setModalVisible(false);
      loadData();
    } catch (error) {
      showError(error.message);
    }
  };

  const handleDelete = async (config) => {
    try {
      await API.delete(`/api/admin/auto_models/${config.name}?user_id=${config.user_id}`);
      showSuccess('Deleted successfully');
      loadData();
    } catch (error) {
      showError(error.message);
    }
  };

  const columns = [
    { title: 'User', dataIndex: ['user', 'username'], key: 'username' },
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Active', dataIndex: 'is_active', key: 'is_active', render: (val) => val ? 'Yes' : 'No' },
    { title: 'Models', dataIndex: 'models', key: 'models', render: models => models.join(', ') },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button icon={<EditOutlined />} onClick={() => {
            setEditingConfig(record);
            form.setFieldsValue({
              ...record,
              models: record.models.join('\n')
            });
            setModalVisible(true);
          }}>Edit</Button>
          <Popconfirm title="Delete this config?" onConfirm={() => handleDelete(record)}>
            <Button danger icon={<DeleteOutlined />}>Delete</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => {
          setEditingConfig(null);
          form.resetFields();
          form.setFieldValue('is_active', true);
          setModalVisible(true);
        }}>
          Add Configuration
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={configs}
        rowKey={record => `${record.user_id}-${record.name}`}
        loading={loading}
        locale={{
          emptyText: 'No auto model configurations found'
        }}
      />

      <Modal
        title={`${editingConfig ? 'Edit' : 'Add'} Configuration`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item name="user_id" label="User" rules={[{ required: true }]}>
            <Select options={users.map(u => ({ value: u.id, label: u.username }))} />
          </Form.Item>
          <Form.Item name="name" label="Name" rules={[{ required: true }]}>
            <Input disabled={!!editingConfig} />
          </Form.Item>
          <Form.Item name="description" label="Description">
            <Input.TextArea />
          </Form.Item>
          <Form.Item name="is_active" label="Active" valuePropName="checked">
            <Switch />
          </Form.Item>
          <Form.Item name="models" label="Models (one per line)" rules={[{ required: true }]}>
            <Input.TextArea rows={8} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
