package controller

import (
	"net/http"
	"one-api/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ListAutoModelConfigs GET /api/auto_models
// @Summary List all auto model configurations
// @Description Get a list of all auto model configurations. Admins see all configurations, regular users see only their own.
// @Tags Auto Models
// @Produce json
// @Success 200 {object} gin.H "success: Whether the request was successful, data: List of auto model configurations"
// @Router /auto_models [get]
func ListAutoModelConfigs(c *gin.Context) {
	userID := c.GetInt("id") // Get user ID from context
	
	var configs []model.AutoModelConfig
	var err error

	// Check if user is admin
	if model.IsAdmin(userID) {
		// Admin gets all configurations
		configs, err = model.GetAllAutoModelConfigsAdmin(model.DB)
	} else {
		// Regular user gets only their own configurations
		configs, err = model.GetAllAutoModelConfigs(model.DB, uint(userID))
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to retrieve auto model configurations: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    configs,
	})
}

// CreateAutoModelConfigRequest represents the request body for creating an auto model configuration
type CreateAutoModelConfigRequest struct {
	Name        string   `json:"name" binding:"required"`
	Description string   `json:"description"`
	IsActive    bool     `json:"is_active"`
	Models      []string `json:"models" binding:"required,min=1"`
}

// CreateAutoModelConfig POST /api/auto_models
// @Summary Create a new auto model configuration
// @Description Create a new auto model configuration
// @Tags Auto Models
// @Accept json
// @Produce json
// @Param config body CreateAutoModelConfigRequest true "Auto model configuration"
// @Success 201 {object} gin.H "success: Whether the request was successful, message: Status message, data: Created configuration"
// @Router /auto_models [post]
func CreateAutoModelConfig(c *gin.Context) {
	userID := c.GetInt("id")
	var req CreateAutoModelConfigRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request payload: " + err.Error(),
		})
		return
	}

	// Check if config with same name already exists for this user
	if _, err := model.GetAutoModelConfig(model.DB, uint(userID), req.Name); err == nil {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": "Auto model configuration with this name already exists",
		})
		return
	}

	// Create the configuration using UpdateAutoModelConfig since it handles both create and update
	err := model.UpdateAutoModelConfig(model.DB, model.UpdateAutoModelConfigRequest{
		Name:        req.Name,
		Description: req.Description,
		IsActive:    req.IsActive,
		Models:      req.Models,
		UserID:      uint(userID),
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create auto model configuration: " + err.Error(),
		})
		return
	}

	// Fetch the created config to return it
	config, err := model.GetAutoModelConfig(model.DB, uint(userID), req.Name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Configuration created but failed to fetch details: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Auto model configuration created successfully",
		"data":    config,
	})
}

// GetAutoModelConfig GET /api/auto_models/:name
// @Summary Get an auto model configuration
// @Description Get an auto model configuration by name for the current user
// @Tags Auto Models
// @Produce json
// @Param name path string true "Auto model name"
// @Success 200 {object} gin.H "success: Whether the request was successful, data: Auto model configuration"
// @Router /auto_models/{name} [get]
func GetAutoModelConfig(c *gin.Context) {
	userID := c.GetInt("id")
	name := c.Param("name")

	config, err := model.GetAutoModelConfig(model.DB, uint(userID), name)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "Auto model configuration not found",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to retrieve auto model configuration: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// UpdateAutoModelConfigRequest represents the request body for updating an auto model configuration
type UpdateAutoModelConfigRequest struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	IsActive    bool     `json:"is_active"`
	Models      []string `json:"models" binding:"required_without=Name,omitempty,min=1"`
}

// UpdateAutoModelConfig PUT /api/auto_models/:name
// @Summary Update an existing auto model configuration
// @Description Update an existing auto model configuration
// @Tags Auto Models
// @Accept json
// @Produce json
// @Param name path string true "Name of the auto model configuration"
// @Param config body UpdateAutoModelConfigRequest true "Updated auto model configuration"
// @Success 200 {object} gin.H "success: Whether the request was successful, message: Status message, data: Updated configuration"
// @Router /auto_models/{name} [put]
func UpdateAutoModelConfig(c *gin.Context) {
	userID := c.GetInt("id")
	name := c.Param("name")

	// Check if config exists
	if _, err := model.GetAutoModelConfig(model.DB, uint(userID), name); err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Auto model configuration not found",
		})
		return
	}

	var req UpdateAutoModelConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request payload: " + err.Error(),
		})
		return
	}

	// If name is being changed, check if new name already exists
	if req.Name != "" && req.Name != name {
		if _, err := model.GetAutoModelConfig(model.DB, uint(userID), req.Name); err == nil {
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"message": "An auto model configuration with this name already exists",
			})
			return
		}
	}

	// Update the configuration
	updateErr := model.UpdateAutoModelConfig(model.DB, model.UpdateAutoModelConfigRequest{
		Name:        req.Name,
		Description: req.Description,
		IsActive:    req.IsActive,
		Models:      req.Models,
		UserID:      uint(userID),
	})

	if updateErr != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to update auto model configuration: " + updateErr.Error(),
		})
		return
	}

	// Determine which name to use for fetching the updated config
	configName := name
	if req.Name != "" {
		configName = req.Name
	}

	// Fetch the updated config to return it
	updatedConfig, fetchErr := model.GetAutoModelConfig(model.DB, uint(userID), configName)
	if fetchErr != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Configuration updated but failed to fetch details: " + fetchErr.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Auto model configuration updated successfully",
		"data":    updatedConfig,
	})
}

// DeleteAutoModelConfig DELETE /api/auto_models/:name
// @Summary Delete an auto model configuration
// @Description Delete an auto model configuration by name for the current user
// @Tags Auto Models
// @Produce json
// @Param name path string true "Auto model name"
// @Success 200 {object} gin.H "success: Whether the request was successful, message: Status message"
// @Router /auto_models/{name} [delete]
func DeleteAutoModelConfig(c *gin.Context) {
	userID := c.GetInt("id") // Assuming user ID is set in context by auth middleware
	name := c.Param("name")

	// Check if config exists for this user
	if _, err := model.GetAutoModelConfig(model.DB, uint(userID), name); err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "Auto model configuration not found",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to retrieve auto model configuration: " + err.Error(),
		})
		return
	}

	err := model.DeleteAutoModelConfig(model.DB, name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete auto model configuration: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Auto model configuration deleted successfully",
	})
}
